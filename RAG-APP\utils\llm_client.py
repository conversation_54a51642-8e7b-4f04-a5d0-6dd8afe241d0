"""
GROQ API client for LLM integration with few-shot prompting
"""
import logging
import os
from typing import List, Dict, Optional, Generator
import time

from groq import Groq

from config import GROQ_API_KEY, LLM_MODEL, FEW_SHOT_EXAMPLES

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GroqLLMClient:
    """Handles GROQ API interactions with few-shot prompting for clinical trials"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = LLM_MODEL):
        self.api_key = api_key or GROQ_API_KEY
        self.model = model
        self.client = None
        self.few_shot_examples = FEW_SHOT_EXAMPLES
        
        if not self.api_key:
            logger.error("GROQ API key not provided. Please set GROQ_API_KEY environment variable.")
            raise ValueError("GROQ API key is required")
    
    def connect(self) -> None:
        """Initialize GROQ client"""
        try:
            self.client = Groq(api_key=self.api_key)
            logger.info("GROQ client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize GROQ client: {str(e)}")
            raise
    
    def _create_few_shot_prompt(self, query: str, context_chunks: List[Dict]) -> str:
        """
        Create a few-shot prompt with examples and context
        
        Args:
            query: User query
            context_chunks: Retrieved relevant chunks
            
        Returns:
            Formatted prompt string
        """
        # System message
        system_prompt = """You are a clinical trial research assistant specializing in dermatology. 
Your role is to provide accurate, evidence-based answers about clinical trials using the provided context.

Guidelines:
- Base your answers strictly on the provided context
- If information is not in the context, clearly state this limitation
- Use specific details from the clinical trials when available
- Maintain a professional, medical tone
- Cite relevant findings with appropriate caveats about study limitations"""
        
        # Few-shot examples
        examples_text = "\n\nHere are some examples of how to respond:\n\n"
        for i, example in enumerate(self.few_shot_examples, 1):
            examples_text += f"Example {i}:\n"
            examples_text += f"Query: {example['query']}\n"
            examples_text += f"Context: {example['context']}\n"
            examples_text += f"Response: {example['response']}\n\n"
        
        # Current context
        context_text = "\n\nRelevant Context from Clinical Trial Documents:\n\n"
        for i, chunk in enumerate(context_chunks, 1):
            context_text += f"Context {i} (from {chunk['source']}):\n"
            context_text += f"{chunk['text']}\n"
            context_text += f"Similarity Score: {chunk.get('similarity_score', 'N/A'):.3f}\n\n"
        
        # Current query
        query_text = f"\n\nCurrent Query: {query}\n\n"
        query_text += "Please provide a comprehensive answer based on the context above. "
        query_text += "If the context doesn't contain sufficient information to answer the query, "
        query_text += "please state this clearly and suggest what additional information might be needed."
        
        # Combine all parts
        full_prompt = system_prompt + examples_text + context_text + query_text
        
        return full_prompt
    
    def generate_response(self, 
                         query: str, 
                         context_chunks: List[Dict],
                         temperature: float = 0.3,
                         max_tokens: int = 1024,
                         stream: bool = False) -> str:
        """
        Generate response using GROQ API with few-shot prompting
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature (0.0 to 1.0)
            max_tokens: Maximum tokens in response
            stream: Whether to stream the response
            
        Returns:
            Generated response text
        """
        if self.client is None:
            self.connect()
        
        try:
            # Create few-shot prompt
            prompt = self._create_few_shot_prompt(query, context_chunks)
            
            # Prepare messages
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            logger.info(f"Generating response for query: {query[:100]}...")
            
            if stream:
                return self._generate_streaming_response(messages, temperature, max_tokens)
            else:
                return self._generate_complete_response(messages, temperature, max_tokens)
                
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return f"I apologize, but I encountered an error while processing your query: {str(e)}"
    
    def _generate_complete_response(self, 
                                  messages: List[Dict], 
                                  temperature: float, 
                                  max_tokens: int) -> str:
        """Generate complete response (non-streaming)"""
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=0.9,
                stream=False,
                stop=None,
            )
            
            response = completion.choices[0].message.content
            logger.info("Response generated successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error in complete response generation: {str(e)}")
            raise
    
    def _generate_streaming_response(self, 
                                   messages: List[Dict], 
                                   temperature: float, 
                                   max_tokens: int) -> Generator[str, None, None]:
        """Generate streaming response"""
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=0.9,
                stream=True,
                stop=None,
            )
            
            for chunk in completion:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Error in streaming response generation: {str(e)}")
            yield f"Error: {str(e)}"
    
    def generate_streaming_response_text(self, 
                                       query: str, 
                                       context_chunks: List[Dict],
                                       temperature: float = 0.3,
                                       max_tokens: int = 1024) -> str:
        """
        Generate streaming response and return complete text
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Returns:
            Complete response text
        """
        if self.client is None:
            self.connect()
        
        try:
            # Create few-shot prompt
            prompt = self._create_few_shot_prompt(query, context_chunks)
            
            # Prepare messages
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            logger.info(f"Generating streaming response for query: {query[:100]}...")
            
            completion = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=0.9,
                stream=True,
                stop=None,
            )
            
            response_text = ""
            for chunk in completion:
                if chunk.choices[0].delta.content:
                    response_text += chunk.choices[0].delta.content
            
            logger.info("Streaming response generated successfully")
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating streaming response: {str(e)}")
            return f"I apologize, but I encountered an error while processing your query: {str(e)}"
    
    def validate_api_key(self) -> bool:
        """Validate GROQ API key by making a test request"""
        if self.client is None:
            self.connect()
        
        try:
            # Make a simple test request
            test_completion = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_completion_tokens=10,
                temperature=0.1
            )
            
            logger.info("API key validation successful")
            return True
            
        except Exception as e:
            logger.error(f"API key validation failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, str]:
        """Get information about the current model"""
        return {
            'model_name': self.model,
            'provider': 'GROQ',
            'api_key_set': bool(self.api_key),
            'client_initialized': self.client is not None
        }


def main():
    """Test the GROQ LLM client"""
    # Initialize client
    try:
        llm_client = GroqLLMClient()
        
        # Validate API key
        if llm_client.validate_api_key():
            print("API key is valid")
        else:
            print("API key validation failed")
            return
        
        # Test query
        test_query = "What are common side effects in dermatology clinical trials?"
        test_context = [
            {
                'text': 'In the dermatology trial, 15% of patients reported mild skin irritation.',
                'source': 'test_study.pdf',
                'similarity_score': 0.85
            }
        ]
        
        # Generate response
        response = llm_client.generate_response(test_query, test_context)
        print(f"Query: {test_query}")
        print(f"Response: {response}")
        
        # Model info
        info = llm_client.get_model_info()
        print(f"Model info: {info}")
        
    except Exception as e:
        print(f"Error testing LLM client: {str(e)}")


if __name__ == "__main__":
    main()
