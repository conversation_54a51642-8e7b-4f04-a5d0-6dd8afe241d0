"""
Quick test to see what's in the processed documents
"""
from utils.document_processor import DocumentProcessor
from utils.embeddings import EmbeddingGenerator
from utils.vector_store import QdrantVectorStore
import numpy as np

def test_documents():
    print("🔍 Testing document processing...")
    
    # Load documents
    processor = DocumentProcessor()
    documents = processor.load_pdf_documents()
    
    print(f"📄 Loaded {len(documents)} documents:")
    for doc in documents:
        print(f"  - {doc['filename']}: {doc['word_count']} words")
    
    # Create chunks
    chunks = processor.chunk_documents(documents)
    print(f"\n📝 Created {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks):
        print(f"  Chunk {i+1}: {len(chunk['text'])} chars from {chunk['source']}")
        print(f"    Preview: {chunk['text'][:100]}...")
        print()
    
    return chunks

def test_embeddings(chunks):
    print("🧠 Testing embeddings...")
    
    # Generate embeddings
    embedding_gen = EmbeddingGenerator()
    embedded_chunks = embedding_gen.embed_chunks(chunks[:3])  # Test first 3
    
    print(f"✅ Generated embeddings for {len(embedded_chunks)} chunks")
    
    # Test similarity
    if len(embedded_chunks) >= 2:
        sim = embedding_gen.compute_similarity(
            embedded_chunks[0]['embedding'], 
            embedded_chunks[1]['embedding']
        )
        print(f"📊 Similarity between chunk 1 and 2: {sim:.3f}")
    
    return embedded_chunks

def test_search(embedded_chunks):
    print("🔍 Testing search...")
    
    # Set up vector store
    vector_store = QdrantVectorStore()
    vector_store.connect()
    vector_store.create_collection(recreate=True)
    vector_store.add_chunks(embedded_chunks)
    
    # Test queries
    embedding_gen = EmbeddingGenerator()
    
    test_queries = [
        "side effects",
        "clinical trial",
        "dermatology",
        "safety",
        "patients"
    ]
    
    for query in test_queries:
        print(f"\n🔎 Testing query: '{query}'")
        query_embedding = embedding_gen.generate_single_embedding(query)
        
        results = vector_store.search_similar(
            query_embedding=query_embedding,
            top_k=3,
            score_threshold=0.1  # Very low threshold
        )
        
        print(f"   Found {len(results)} results:")
        for result in results:
            print(f"   - Score: {result['similarity_score']:.3f} from {result['source']}")
            print(f"     Text: {result['text'][:100]}...")

if __name__ == "__main__":
    chunks = test_documents()
    if chunks:
        embedded_chunks = test_embeddings(chunks)
        if embedded_chunks:
            test_search(embedded_chunks)
    else:
        print("❌ No chunks to test")
