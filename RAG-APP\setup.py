"""
Setup script for the Clinical Trial RAG Application
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False


def setup_environment():
    """Set up environment configuration"""
    print("🔧 Setting up environment...")
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        if os.path.exists(".env.template"):
            shutil.copy(".env.template", ".env")
            print("✅ Created .env file from template")
            print("⚠️  Please edit .env file and add your GROQ API key")
        else:
            # Create basic .env file
            with open(".env", "w") as f:
                f.write("# GROQ API Key - Get from https://console.groq.com/\n")
                f.write("GROQ_API_KEY=your_groq_api_key_here\n")
            print("✅ Created .env file")
            print("⚠️  Please edit .env file and add your GROQ API key")
    else:
        print("✅ .env file already exists")
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    print("✅ Created logs directory")
    
    return True


def check_documents():
    """Check if clinical trial documents exist"""
    print("📄 Checking for clinical trial documents...")
    
    docs_folder = Path("Clinical Trial Research Reports")
    if not docs_folder.exists():
        print(f"❌ Documents folder not found: {docs_folder}")
        return False
    
    pdf_files = list(docs_folder.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found in {docs_folder}")
        return False
    
    print(f"✅ Found {len(pdf_files)} PDF documents")
    for pdf_file in pdf_files:
        print(f"   - {pdf_file.name}")
    
    return True


def check_qdrant():
    """Check Qdrant installation and provide setup instructions"""
    print("🔍 Checking Qdrant setup...")
    
    try:
        import qdrant_client
        print("✅ Qdrant client installed")
        
        # Try to connect to local Qdrant
        try:
            client = qdrant_client.QdrantClient("localhost", port=6333)
            collections = client.get_collections()
            print("✅ Connected to local Qdrant instance")
            return True
        except Exception:
            print("⚠️  Could not connect to local Qdrant instance")
            print("   You can either:")
            print("   1. Install Qdrant locally: https://qdrant.tech/documentation/quick-start/")
            print("   2. Use Qdrant Cloud: https://cloud.qdrant.io/")
            print("   3. The application will use in-memory storage as fallback")
            return True
            
    except ImportError:
        print("❌ Qdrant client not installed")
        return False


def validate_setup():
    """Validate the complete setup"""
    print("\n🔍 Validating setup...")
    
    try:
        # Import main modules to check for issues
        from utils.document_processor import DocumentProcessor
        from utils.embeddings import EmbeddingGenerator
        from utils.vector_store import QdrantVectorStore
        from utils.llm_client import GroqLLMClient
        from utils.rag_system import RAGSystem
        
        print("✅ All modules imported successfully")
        
        # Check environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        groq_key = os.getenv("GROQ_API_KEY")
        if groq_key and groq_key != "your_groq_api_key_here":
            print("✅ GROQ API key configured")
        else:
            print("⚠️  GROQ API key not configured")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 Clinical Trial RAG Application Setup")
    print("=" * 50)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Install requirements
    if success and not install_requirements():
        success = False
    
    # Setup environment
    if success and not setup_environment():
        success = False
    
    # Check documents
    if success and not check_documents():
        success = False
    
    # Check Qdrant
    if success and not check_qdrant():
        success = False
    
    # Validate setup
    if success and not validate_setup():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file and add your GROQ API key")
        print("2. Start Qdrant (optional, will use in-memory if not available)")
        print("3. Run the application: streamlit run app.py")
    else:
        print("❌ Setup encountered errors. Please fix the issues above.")
    
    return success


if __name__ == "__main__":
    main()
