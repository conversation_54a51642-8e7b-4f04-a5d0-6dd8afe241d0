"""
Document processing utilities for RAG application
Handles PDF loading, text extraction, and paragraph-based chunking
"""
import os
import re
import logging
from typing import List, Dict, Tuple
from pathlib import Path

import PyPDF2
import pdfplumber
from tqdm import tqdm

from config import DOCUMENTS_FOLDER, CHUNK_SIZE, CHUNK_OVERLAP

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Handles document loading, processing, and chunking"""
    
    def __init__(self, documents_folder: str = DOCUMENTS_FOLDER):
        self.documents_folder = documents_folder
        self.chunk_size = CHUNK_SIZE
        self.chunk_overlap = CHUNK_OVERLAP
    
    def load_pdf_documents(self) -> List[Dict[str, str]]:
        """
        Load all PDF documents from the specified folder
        
        Returns:
            List of dictionaries containing document metadata and content
        """
        documents = []
        pdf_files = self._get_pdf_files()
        
        logger.info(f"Found {len(pdf_files)} PDF files to process")
        
        for pdf_file in tqdm(pdf_files, desc="Loading PDF documents"):
            try:
                content = self._extract_text_from_pdf(pdf_file)
                if content.strip():  # Only add non-empty documents
                    documents.append({
                        'filename': pdf_file.name,
                        'filepath': str(pdf_file),
                        'content': content,
                        'word_count': len(content.split())
                    })
                    logger.info(f"Successfully loaded {pdf_file.name} ({len(content.split())} words)")
                else:
                    logger.warning(f"No text extracted from {pdf_file.name}")
            except Exception as e:
                logger.error(f"Error processing {pdf_file.name}: {str(e)}")
        
        logger.info(f"Successfully loaded {len(documents)} documents")
        return documents
    
    def _get_pdf_files(self) -> List[Path]:
        """Get list of PDF files in the documents folder"""
        folder_path = Path(self.documents_folder)
        if not folder_path.exists():
            raise FileNotFoundError(f"Documents folder not found: {self.documents_folder}")
        
        pdf_files = list(folder_path.glob("*.pdf"))
        return pdf_files
    
    def _extract_text_from_pdf(self, pdf_path: Path) -> str:
        """
        Extract text from PDF using pdfplumber (primary) with PyPDF2 fallback
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text content
        """
        try:
            # Try pdfplumber first (better for complex layouts)
            with pdfplumber.open(pdf_path) as pdf:
                text = ""
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                
                if text.strip():
                    return self._clean_text(text)
        except Exception as e:
            logger.warning(f"pdfplumber failed for {pdf_path.name}, trying PyPDF2: {str(e)}")
        
        try:
            # Fallback to PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                
                return self._clean_text(text)
        except Exception as e:
            logger.error(f"Both PDF extraction methods failed for {pdf_path.name}: {str(e)}")
            return ""
    
    def _clean_text(self, text: str) -> str:
        """
        Clean and normalize extracted text
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\.\,\;\:\!\?\-\(\)\[\]\"\'\/]', '', text)
        
        # Fix common PDF extraction issues
        text = text.replace('ﬁ', 'fi').replace('ﬂ', 'fl')  # Fix ligatures
        text = re.sub(r'(\w)-\s+(\w)', r'\1\2', text)  # Fix hyphenated words across lines
        
        return text.strip()
    
    def chunk_documents(self, documents: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        Chunk documents into paragraph-based segments
        
        Args:
            documents: List of document dictionaries
            
        Returns:
            List of chunk dictionaries
        """
        all_chunks = []
        
        for doc in tqdm(documents, desc="Chunking documents"):
            chunks = self._create_paragraph_chunks(doc['content'], doc['filename'])
            all_chunks.extend(chunks)
            logger.info(f"Created {len(chunks)} chunks from {doc['filename']}")
        
        logger.info(f"Total chunks created: {len(all_chunks)}")
        return all_chunks
    
    def _create_paragraph_chunks(self, text: str, filename: str) -> List[Dict[str, str]]:
        """
        Create paragraph-based chunks with overlap
        
        Args:
            text: Document text
            filename: Source filename
            
        Returns:
            List of chunk dictionaries
        """
        # Split into paragraphs (double newline or single newline with significant content)
        paragraphs = re.split(r'\n\s*\n|\n(?=[A-Z][a-z])', text)
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        chunks = []
        current_chunk = ""
        chunk_id = 0
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                chunks.append(self._create_chunk_dict(current_chunk, filename, chunk_id))
                chunk_id += 1
                
                # Start new chunk with overlap from previous chunk
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + " " + paragraph if overlap_text else paragraph
            else:
                current_chunk += " " + paragraph if current_chunk else paragraph
        
        # Add the last chunk if it has content
        if current_chunk.strip():
            chunks.append(self._create_chunk_dict(current_chunk, filename, chunk_id))
        
        return chunks
    
    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text from the end of current chunk"""
        if len(text) <= self.chunk_overlap:
            return text
        
        # Try to find a sentence boundary for natural overlap
        overlap_start = len(text) - self.chunk_overlap
        sentence_end = text.rfind('.', overlap_start)
        
        if sentence_end > overlap_start:
            return text[sentence_end + 1:].strip()
        else:
            return text[-self.chunk_overlap:].strip()
    
    def _create_chunk_dict(self, text: str, filename: str, chunk_id: int) -> Dict[str, str]:
        """Create a standardized chunk dictionary"""
        return {
            'id': f"{filename}_{chunk_id}",
            'text': text.strip(),
            'source': filename,
            'chunk_id': chunk_id,
            'word_count': len(text.split()),
            'char_count': len(text)
        }


def main():
    """Test the document processor"""
    processor = DocumentProcessor()
    
    # Load documents
    documents = processor.load_pdf_documents()
    print(f"Loaded {len(documents)} documents")
    
    # Create chunks
    chunks = processor.chunk_documents(documents)
    print(f"Created {len(chunks)} chunks")
    
    # Display sample chunk
    if chunks:
        sample_chunk = chunks[0]
        print(f"\nSample chunk from {sample_chunk['source']}:")
        print(f"Text: {sample_chunk['text'][:200]}...")
        print(f"Word count: {sample_chunk['word_count']}")


if __name__ == "__main__":
    main()
