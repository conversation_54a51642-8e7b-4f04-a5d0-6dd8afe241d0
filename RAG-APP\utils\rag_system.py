"""
RAG (Retrieval-Augmented Generation) system that combines document retrieval with LLM generation
"""
import logging
from typing import List, Dict, Optional, Tuple
import time

from .document_processor import DocumentProcessor
from .embeddings import EmbeddingGenerator
from .vector_store import QdrantVectorStore
from .llm_client import <PERSON>roq<PERSON><PERSON>lient
from config import TOP_K_RESULTS, SIMILARITY_THRESHOLD

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RAGSystem:
    """Complete RAG system for clinical trial document analysis"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()
        self.embedding_generator = EmbeddingGenerator()
        self.vector_store = QdrantVectorStore()
        self.llm_client = GroqLLMClient()
        self.is_initialized = False
    
    def initialize(self, recreate_collection: bool = False) -> None:
        """
        Initialize all components of the RAG system
        
        Args:
            recreate_collection: Whether to recreate the vector collection
        """
        try:
            logger.info("Initializing RAG system...")
            
            # Initialize embedding generator
            self.embedding_generator.load_model()
            
            # Initialize vector store
            self.vector_store.connect()
            self.vector_store.create_collection(recreate=recreate_collection)
            
            # Initialize LLM client
            self.llm_client.connect()
            
            self.is_initialized = True
            logger.info("RAG system initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing RAG system: {str(e)}")
            raise
    
    def load_and_process_documents(self) -> Dict[str, int]:
        """
        Load documents, create embeddings, and store in vector database
        
        Returns:
            Dictionary with processing statistics
        """
        if not self.is_initialized:
            self.initialize()
        
        try:
            logger.info("Starting document processing pipeline...")
            start_time = time.time()
            
            # Step 1: Load PDF documents
            logger.info("Loading PDF documents...")
            documents = self.document_processor.load_pdf_documents()
            
            if not documents:
                logger.warning("No documents loaded")
                return {'documents': 0, 'chunks': 0, 'processing_time': 0}
            
            # Step 2: Create chunks
            logger.info("Creating document chunks...")
            chunks = self.document_processor.chunk_documents(documents)
            
            if not chunks:
                logger.warning("No chunks created")
                return {'documents': len(documents), 'chunks': 0, 'processing_time': 0}
            
            # Step 3: Generate embeddings
            logger.info("Generating embeddings...")
            embedded_chunks = self.embedding_generator.embed_chunks(chunks)
            
            # Step 4: Store in vector database
            logger.info("Storing chunks in vector database...")
            self.vector_store.add_chunks(embedded_chunks)
            
            processing_time = time.time() - start_time
            
            stats = {
                'documents': len(documents),
                'chunks': len(embedded_chunks),
                'processing_time': processing_time
            }
            
            logger.info(f"Document processing completed: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error in document processing pipeline: {str(e)}")
            raise
    
    def query(self, 
              user_query: str, 
              top_k: int = TOP_K_RESULTS,
              similarity_threshold: float = SIMILARITY_THRESHOLD,
              source_filter: Optional[str] = None,
              include_context: bool = True) -> Dict:
        """
        Process a user query through the RAG pipeline
        
        Args:
            user_query: User's question
            top_k: Number of similar chunks to retrieve
            similarity_threshold: Minimum similarity score
            source_filter: Optional filter by source document
            include_context: Whether to include retrieved context in response
            
        Returns:
            Dictionary with query results
        """
        if not self.is_initialized:
            self.initialize()
        
        try:
            logger.info(f"Processing query: {user_query[:100]}...")
            start_time = time.time()
            
            # Step 1: Generate query embedding
            query_embedding = self.embedding_generator.generate_single_embedding(user_query)
            
            # Step 2: Retrieve similar chunks
            similar_chunks = self.vector_store.search_similar(
                query_embedding=query_embedding,
                top_k=top_k,
                score_threshold=similarity_threshold,
                source_filter=source_filter
            )
            
            if not similar_chunks:
                return {
                    'query': user_query,
                    'response': "I couldn't find any relevant information in the clinical trial documents for your query. Please try rephrasing your question or check if the documents contain information about this topic.",
                    'retrieved_chunks': [],
                    'processing_time': time.time() - start_time,
                    'sources': []
                }
            
            # Step 3: Generate response using LLM
            response = self.llm_client.generate_response(
                query=user_query,
                context_chunks=similar_chunks
            )
            
            # Step 4: Prepare results
            processing_time = time.time() - start_time
            sources = list(set([chunk['source'] for chunk in similar_chunks]))
            
            result = {
                'query': user_query,
                'response': response,
                'retrieved_chunks': similar_chunks if include_context else [],
                'processing_time': processing_time,
                'sources': sources,
                'num_chunks_retrieved': len(similar_chunks)
            }
            
            logger.info(f"Query processed successfully in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                'query': user_query,
                'response': f"I encountered an error while processing your query: {str(e)}",
                'retrieved_chunks': [],
                'processing_time': 0,
                'sources': [],
                'error': str(e)
            }
    
    def get_available_sources(self) -> List[str]:
        """Get list of available source documents"""
        if not self.is_initialized:
            self.initialize()
        
        try:
            return self.vector_store.get_all_sources()
        except Exception as e:
            logger.error(f"Error getting available sources: {str(e)}")
            return []
    
    def get_system_status(self) -> Dict:
        """Get status of all system components"""
        status = {
            'initialized': self.is_initialized,
            'embedding_model': None,
            'vector_store': None,
            'llm_model': None
        }
        
        try:
            if self.is_initialized:
                # Embedding model info
                status['embedding_model'] = self.embedding_generator.get_model_info()
                
                # Vector store info
                status['vector_store'] = self.vector_store.get_collection_info()
                
                # LLM model info
                status['llm_model'] = self.llm_client.get_model_info()
                
        except Exception as e:
            logger.error(f"Error getting system status: {str(e)}")
            status['error'] = str(e)
        
        return status
    
    def validate_setup(self) -> Dict[str, bool]:
        """Validate that all components are properly set up"""
        validation = {
            'embedding_model': False,
            'vector_store': False,
            'llm_client': False,
            'documents_folder': False
        }
        
        try:
            # Check embedding model
            if self.embedding_generator.model is not None:
                validation['embedding_model'] = True
            
            # Check vector store
            if self.vector_store.client is not None:
                validation['vector_store'] = True
            
            # Check LLM client
            validation['llm_client'] = self.llm_client.validate_api_key()
            
            # Check documents folder
            import os
            validation['documents_folder'] = os.path.exists(self.document_processor.documents_folder)
            
        except Exception as e:
            logger.error(f"Error validating setup: {str(e)}")
        
        return validation
    
    def reset_system(self) -> None:
        """Reset the system by clearing the vector database"""
        try:
            logger.info("Resetting RAG system...")
            self.vector_store.delete_collection()
            self.is_initialized = False
            logger.info("RAG system reset successfully")
        except Exception as e:
            logger.error(f"Error resetting system: {str(e)}")
            raise


def main():
    """Test the RAG system"""
    # Initialize RAG system
    rag = RAGSystem()
    
    # Validate setup
    validation = rag.validate_setup()
    print(f"Setup validation: {validation}")
    
    if not all(validation.values()):
        print("Some components are not properly configured")
        return
    
    # Initialize system
    rag.initialize(recreate_collection=True)
    
    # Load and process documents
    stats = rag.load_and_process_documents()
    print(f"Processing stats: {stats}")
    
    # Test query
    test_query = "What are the common side effects reported in dermatology clinical trials?"
    result = rag.query(test_query)
    
    print(f"\nQuery: {result['query']}")
    print(f"Response: {result['response'][:200]}...")
    print(f"Sources: {result['sources']}")
    print(f"Processing time: {result['processing_time']:.2f}s")


if __name__ == "__main__":
    main()
