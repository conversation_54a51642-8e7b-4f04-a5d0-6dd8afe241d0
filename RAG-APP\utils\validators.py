"""
Validation utilities for the RAG application
"""
import os
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import logging

logger = logging.getLogger("RAG_APP")


class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass


def validate_environment() -> Dict[str, bool]:
    """
    Validate environment setup and dependencies
    
    Returns:
        Dictionary with validation results
    """
    validation_results = {
        'groq_api_key': False,
        'documents_folder': False,
        'python_version': False,
        'required_packages': False
    }
    
    try:
        # Check GROQ API key
        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key and len(groq_key.strip()) > 0:
            validation_results['groq_api_key'] = True
        
        # Check documents folder
        from config import DOCUMENTS_FOLDER
        if os.path.exists(DOCUMENTS_FOLDER) and os.path.isdir(DOCUMENTS_FOLDER):
            pdf_files = list(Path(DOCUMENTS_FOLDER).glob("*.pdf"))
            if pdf_files:
                validation_results['documents_folder'] = True
        
        # Check Python version
        import sys
        if sys.version_info >= (3, 8):
            validation_results['python_version'] = True
        
        # Check required packages
        try:
            import streamlit
            import qdrant_client
            import sentence_transformers
            import groq
            import PyPDF2
            validation_results['required_packages'] = True
        except ImportError as e:
            logger.warning(f"Missing required package: {e}")
    
    except Exception as e:
        logger.error(f"Error during environment validation: {e}")
    
    return validation_results


def validate_query(query: str) -> Tuple[bool, Optional[str]]:
    """
    Validate user query
    
    Args:
        query: User input query
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not query or not query.strip():
        return False, "Query cannot be empty"
    
    if len(query.strip()) < 3:
        return False, "Query must be at least 3 characters long"
    
    if len(query) > 1000:
        return False, "Query is too long (maximum 1000 characters)"
    
    # Check for potentially harmful content
    harmful_patterns = [
        r'<script.*?>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'exec\s*\('
    ]
    
    for pattern in harmful_patterns:
        if re.search(pattern, query, re.IGNORECASE):
            return False, "Query contains potentially harmful content"
    
    return True, None


def validate_file_path(file_path: str) -> Tuple[bool, Optional[str]]:
    """
    Validate file path for security
    
    Args:
        file_path: Path to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not file_path:
        return False, "File path cannot be empty"
    
    # Check for path traversal attempts
    if '..' in file_path or file_path.startswith('/'):
        return False, "Invalid file path"
    
    # Check file extension
    allowed_extensions = ['.pdf', '.txt', '.md']
    file_ext = Path(file_path).suffix.lower()
    if file_ext not in allowed_extensions:
        return False, f"File type not allowed. Allowed types: {allowed_extensions}"
    
    return True, None


def validate_api_response(response: Dict) -> Tuple[bool, Optional[str]]:
    """
    Validate API response structure
    
    Args:
        response: API response dictionary
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    required_fields = ['query', 'response', 'retrieved_chunks', 'sources']
    
    for field in required_fields:
        if field not in response:
            return False, f"Missing required field: {field}"
    
    # Validate response content
    if not isinstance(response['response'], str) or not response['response'].strip():
        return False, "Invalid response content"
    
    if not isinstance(response['retrieved_chunks'], list):
        return False, "Invalid retrieved chunks format"
    
    if not isinstance(response['sources'], list):
        return False, "Invalid sources format"
    
    return True, None


def validate_embedding_dimensions(embeddings, expected_dim: int = 384) -> Tuple[bool, Optional[str]]:
    """
    Validate embedding dimensions
    
    Args:
        embeddings: Embedding array or list
        expected_dim: Expected dimension size
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        import numpy as np
        
        if isinstance(embeddings, list):
            embeddings = np.array(embeddings)
        
        if embeddings.ndim == 1:
            if embeddings.shape[0] != expected_dim:
                return False, f"Invalid embedding dimension: {embeddings.shape[0]}, expected {expected_dim}"
        elif embeddings.ndim == 2:
            if embeddings.shape[1] != expected_dim:
                return False, f"Invalid embedding dimension: {embeddings.shape[1]}, expected {expected_dim}"
        else:
            return False, f"Invalid embedding shape: {embeddings.shape}"
        
        # Check for NaN or infinite values
        if np.isnan(embeddings).any() or np.isinf(embeddings).any():
            return False, "Embeddings contain NaN or infinite values"
        
        return True, None
        
    except Exception as e:
        return False, f"Error validating embeddings: {str(e)}"


def sanitize_text(text: str) -> str:
    """
    Sanitize text input for safe processing
    
    Args:
        text: Input text to sanitize
        
    Returns:
        Sanitized text
    """
    if not text:
        return ""
    
    # Remove potentially harmful HTML/JavaScript
    import html
    text = html.escape(text)
    
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Remove control characters except newlines and tabs
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    return text


def validate_chunk_data(chunk: Dict) -> Tuple[bool, Optional[str]]:
    """
    Validate document chunk data structure
    
    Args:
        chunk: Chunk dictionary to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    required_fields = ['id', 'text', 'source', 'chunk_id']
    
    for field in required_fields:
        if field not in chunk:
            return False, f"Missing required field in chunk: {field}"
    
    # Validate text content
    if not isinstance(chunk['text'], str) or not chunk['text'].strip():
        return False, "Chunk text is empty or invalid"
    
    # Validate chunk ID
    if not isinstance(chunk['chunk_id'], (int, str)):
        return False, "Invalid chunk ID type"
    
    # Validate source
    if not isinstance(chunk['source'], str) or not chunk['source'].strip():
        return False, "Invalid source information"
    
    return True, None


class ConfigValidator:
    """Validator for application configuration"""
    
    @staticmethod
    def validate_config() -> Dict[str, bool]:
        """Validate application configuration"""
        validation_results = {}
        
        try:
            from config import (
                EMBEDDING_MODEL, LLM_MODEL, QDRANT_HOST, QDRANT_PORT,
                COLLECTION_NAME, VECTOR_SIZE, DOCUMENTS_FOLDER
            )
            
            # Validate embedding model
            validation_results['embedding_model'] = bool(EMBEDDING_MODEL and isinstance(EMBEDDING_MODEL, str))
            
            # Validate LLM model
            validation_results['llm_model'] = bool(LLM_MODEL and isinstance(LLM_MODEL, str))
            
            # Validate Qdrant configuration
            validation_results['qdrant_host'] = bool(QDRANT_HOST and isinstance(QDRANT_HOST, str))
            validation_results['qdrant_port'] = isinstance(QDRANT_PORT, int) and 1 <= QDRANT_PORT <= 65535
            
            # Validate collection name
            validation_results['collection_name'] = bool(COLLECTION_NAME and isinstance(COLLECTION_NAME, str))
            
            # Validate vector size
            validation_results['vector_size'] = isinstance(VECTOR_SIZE, int) and VECTOR_SIZE > 0
            
            # Validate documents folder
            validation_results['documents_folder'] = bool(DOCUMENTS_FOLDER and isinstance(DOCUMENTS_FOLDER, str))
            
        except ImportError as e:
            logger.error(f"Error importing configuration: {e}")
            validation_results['config_import'] = False
        
        return validation_results
