"""
Qdrant vector database utilities for storing and retrieving embeddings
"""
import logging
import uuid
from typing import List, Dict, Optional, Tuple
import numpy as np

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import (
    Distance, VectorParams, C<PERSON><PERSON>oll<PERSON>tion, PointStruct,
    Filter, FieldCondition, SearchRequest, HnswConfigDiff
)

from config import (
    QDRANT_HOST, QDRANT_PORT, COLLECTION_NAME, 
    VECTOR_SIZE, TOP_K_RESULTS, SIMILARITY_THRESHOLD
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QdrantVectorStore:
    """Handles Qdrant vector database operations"""
    
    def __init__(self, 
                 host: str = QDRANT_HOST,
                 port: int = QDRANT_PORT,
                 collection_name: str = COLLECTION_NAME):
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.client = None
        self.vector_size = VECTOR_SIZE
    
    def connect(self) -> None:
        """Connect to Qdrant database"""
        try:
            self.client = QdrantClient(host=self.host, port=self.port)
            logger.info(f"Connected to Qdrant at {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {str(e)}")
            # Fallback to in-memory client for development
            logger.info("Falling back to in-memory Qdrant client")
            self.client = QdrantClient(":memory:")
    
    def create_collection(self, recreate: bool = False) -> None:
        """
        Create collection with HNSW indexing
        
        Args:
            recreate: Whether to recreate collection if it exists
        """
        if self.client is None:
            self.connect()
        
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_exists = any(col.name == self.collection_name for col in collections)
            
            if collection_exists and recreate:
                logger.info(f"Deleting existing collection: {self.collection_name}")
                self.client.delete_collection(self.collection_name)
                collection_exists = False
            
            if not collection_exists:
                logger.info(f"Creating collection: {self.collection_name}")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE,
                        hnsw_config=HnswConfigDiff(
                            m=16,  # Number of bi-directional links for each node
                            ef_construct=200,  # Size of the dynamic candidate list
                            full_scan_threshold=10000,  # Threshold for full scan
                        )
                    ),
                    optimizers_config=models.OptimizersConfigDiff(
                        default_segment_number=2,
                        max_segment_size=None,
                        memmap_threshold=None,
                        indexing_threshold=20000,
                        flush_interval_sec=5,
                        max_optimization_threads=1
                    )
                )
                logger.info("Collection created successfully with HNSW indexing")
            else:
                logger.info(f"Collection {self.collection_name} already exists")
                
        except Exception as e:
            logger.error(f"Error creating collection: {str(e)}")
            raise
    
    def add_chunks(self, chunks: List[Dict]) -> None:
        """
        Add document chunks with embeddings to the vector store
        
        Args:
            chunks: List of chunk dictionaries with embeddings
        """
        if self.client is None:
            self.connect()
        
        if not chunks:
            logger.warning("No chunks provided to add")
            return
        
        try:
            points = []
            for chunk in chunks:
                if 'embedding' not in chunk:
                    logger.warning(f"Chunk {chunk.get('id', 'unknown')} missing embedding")
                    continue
                
                # Create point for Qdrant
                point = PointStruct(
                    id=str(uuid.uuid4()),  # Generate unique ID
                    vector=chunk['embedding'].tolist(),
                    payload={
                        'chunk_id': chunk['id'],
                        'text': chunk['text'],
                        'source': chunk['source'],
                        'chunk_number': chunk['chunk_id'],
                        'word_count': chunk['word_count'],
                        'char_count': chunk['char_count']
                    }
                )
                points.append(point)
            
            if points:
                # Upload points in batches
                batch_size = 100
                for i in range(0, len(points), batch_size):
                    batch = points[i:i + batch_size]
                    self.client.upsert(
                        collection_name=self.collection_name,
                        points=batch
                    )
                    logger.info(f"Uploaded batch {i//batch_size + 1}/{(len(points)-1)//batch_size + 1}")
                
                logger.info(f"Successfully added {len(points)} chunks to vector store")
            else:
                logger.warning("No valid chunks to add (all missing embeddings)")
                
        except Exception as e:
            logger.error(f"Error adding chunks to vector store: {str(e)}")
            raise
    
    def search_similar(self, 
                      query_embedding: np.ndarray, 
                      top_k: int = TOP_K_RESULTS,
                      score_threshold: float = SIMILARITY_THRESHOLD,
                      source_filter: Optional[str] = None) -> List[Dict]:
        """
        Search for similar chunks using vector similarity
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of results to return
            score_threshold: Minimum similarity score
            source_filter: Optional filter by source document
            
        Returns:
            List of similar chunks with metadata
        """
        if self.client is None:
            self.connect()
        
        try:
            # Prepare search filter
            search_filter = None
            if source_filter:
                search_filter = Filter(
                    must=[
                        FieldCondition(
                            key="source",
                            match=models.MatchValue(value=source_filter)
                        )
                    ]
                )
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),
                query_filter=search_filter,
                limit=top_k,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    'id': result.payload['chunk_id'],
                    'text': result.payload['text'],
                    'source': result.payload['source'],
                    'chunk_number': result.payload['chunk_number'],
                    'word_count': result.payload['word_count'],
                    'similarity_score': result.score,
                    'point_id': result.id
                })
            
            logger.info(f"Found {len(results)} similar chunks")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            return []
    
    def get_collection_info(self) -> Dict:
        """Get information about the collection"""
        if self.client is None:
            self.connect()
        
        try:
            collection_info = self.client.get_collection(self.collection_name)
            return {
                'name': collection_info.config.params.vectors.size,
                'vector_size': collection_info.config.params.vectors.size,
                'distance': collection_info.config.params.vectors.distance,
                'points_count': collection_info.points_count,
                'segments_count': collection_info.segments_count,
                'status': collection_info.status
            }
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            return {}
    
    def delete_collection(self) -> None:
        """Delete the collection"""
        if self.client is None:
            self.connect()
        
        try:
            self.client.delete_collection(self.collection_name)
            logger.info(f"Deleted collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error deleting collection: {str(e)}")
    
    def get_all_sources(self) -> List[str]:
        """Get list of all unique source documents in the collection"""
        if self.client is None:
            self.connect()
        
        try:
            # Scroll through all points to get unique sources
            sources = set()
            offset = None
            
            while True:
                results, offset = self.client.scroll(
                    collection_name=self.collection_name,
                    limit=100,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )
                
                if not results:
                    break
                
                for point in results:
                    if 'source' in point.payload:
                        sources.add(point.payload['source'])
            
            return sorted(list(sources))
            
        except Exception as e:
            logger.error(f"Error getting sources: {str(e)}")
            return []


def main():
    """Test the vector store"""
    # Initialize vector store
    vector_store = QdrantVectorStore()
    
    # Connect and create collection
    vector_store.connect()
    vector_store.create_collection(recreate=True)
    
    # Test data
    test_chunks = [
        {
            'id': 'test_1',
            'text': 'Clinical trials in dermatology focus on skin conditions.',
            'source': 'test_doc.pdf',
            'chunk_id': 0,
            'word_count': 8,
            'char_count': 50,
            'embedding': np.random.rand(384)  # Mock embedding
        }
    ]
    
    # Add chunks
    vector_store.add_chunks(test_chunks)
    
    # Get collection info
    info = vector_store.get_collection_info()
    print(f"Collection info: {info}")


if __name__ == "__main__":
    main()
