"""
Test script to verify RAG application setup
"""
import os
import sys
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import streamlit
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import qdrant_client
        print("✅ Qdrant client imported successfully")
    except ImportError as e:
        print(f"❌ Qdrant client import failed: {e}")
        return False
    
    try:
        import sentence_transformers
        print("✅ Sentence transformers imported successfully")
    except ImportError as e:
        print(f"❌ Sentence transformers import failed: {e}")
        return False
    
    try:
        import groq
        print("✅ GROQ imported successfully")
    except ImportError as e:
        print(f"❌ GROQ import failed: {e}")
        return False
    
    try:
        import PyPDF2
        import pdfplumber
        print("✅ PDF processing libraries imported successfully")
    except ImportError as e:
        print(f"❌ PDF libraries import failed: {e}")
        return False
    
    return True


def test_environment():
    """Test environment configuration"""
    print("\n🔧 Testing environment...")
    
    # Check .env file
    if not os.path.exists(".env"):
        print("❌ .env file not found")
        return False
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ .env file loaded")
    except Exception as e:
        print(f"❌ Error loading .env file: {e}")
        return False
    
    # Check GROQ API key
    groq_key = os.getenv("GROQ_API_KEY")
    if not groq_key:
        print("❌ GROQ_API_KEY not found in environment")
        return False
    elif groq_key == "your_groq_api_key_here":
        print("❌ GROQ_API_KEY not configured (still has placeholder value)")
        return False
    else:
        print("✅ GROQ_API_KEY configured")
    
    return True


def test_documents():
    """Test document availability"""
    print("\n📄 Testing documents...")
    
    docs_folder = Path("Clinical Trial Research Reports")
    if not docs_folder.exists():
        print(f"❌ Documents folder not found: {docs_folder}")
        return False
    
    pdf_files = list(docs_folder.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found in {docs_folder}")
        return False
    
    print(f"✅ Found {len(pdf_files)} PDF documents")
    return True


def test_qdrant_connection():
    """Test Qdrant connection"""
    print("\n🔗 Testing Qdrant connection...")
    
    try:
        from qdrant_client import QdrantClient
        
        # Try local connection first
        try:
            client = QdrantClient("localhost", port=6333, timeout=2)
            client.get_collections()
            print("✅ Connected to local Qdrant instance")
            return True
        except Exception:
            print("⚠️  Local Qdrant not available, will use in-memory storage")
            
        # Test in-memory client
        try:
            client = QdrantClient(":memory:")
            print("✅ In-memory Qdrant client working")
            return True
        except Exception as e:
            print(f"❌ In-memory Qdrant client failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Qdrant client error: {e}")
        return False


def test_groq_api():
    """Test GROQ API connection"""
    print("\n🤖 Testing GROQ API...")
    
    try:
        from groq import Groq
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key or api_key == "your_groq_api_key_here":
            print("❌ GROQ API key not configured")
            return False
        
        client = Groq(api_key=api_key)
        
        # Test with a simple request
        completion = client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=[{"role": "user", "content": "Hello"}],
            max_completion_tokens=10,
            temperature=0.1
        )
        
        print("✅ GROQ API connection successful")
        return True
        
    except Exception as e:
        print(f"❌ GROQ API test failed: {e}")
        return False


def test_embedding_model():
    """Test embedding model loading"""
    print("\n🧠 Testing embedding model...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        
        # Test embedding generation
        test_text = "This is a test sentence."
        embedding = model.encode([test_text])
        
        print(f"✅ Embedding model loaded successfully")
        print(f"   Embedding dimension: {embedding.shape[1]}")
        return True
        
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 RAG Application Setup Test")
    print("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("Environment", test_environment),
        ("Documents", test_documents),
        ("Qdrant Connection", test_qdrant_connection),
        ("GROQ API", test_groq_api),
        ("Embedding Model", test_embedding_model),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! You can run the Streamlit app:")
        print("   streamlit run app.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues above before running the app.")
    
    return all_passed


if __name__ == "__main__":
    main()
