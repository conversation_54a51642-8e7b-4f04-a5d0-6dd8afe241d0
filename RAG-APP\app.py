"""
Streamlit web application for Clinical Trial RAG Assistant
"""
import streamlit as st
import time
import os
from typing import Dict, List

# Import RAG system components
from utils.rag_system import RAGSystem
from config import PAGE_TITLE, PAGE_ICON, TOP_K_RESULTS, SIMILARITY_THRESHOLD

# Configure Streamlit page
st.set_page_config(
    page_title=PAGE_TITLE,
    page_icon=PAGE_ICON,
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'rag_system' not in st.session_state:
    st.session_state.rag_system = None
if 'system_initialized' not in st.session_state:
    st.session_state.system_initialized = False
if 'documents_processed' not in st.session_state:
    st.session_state.documents_processed = False
if 'processing_stats' not in st.session_state:
    st.session_state.processing_stats = {}
if 'query_history' not in st.session_state:
    st.session_state.query_history = []


def initialize_system():
    """Initialize the RAG system"""
    try:
        with st.spinner("Initializing RAG system..."):
            st.session_state.rag_system = RAGSystem()
            st.session_state.rag_system.initialize()
            st.session_state.system_initialized = True
        st.success("RAG system initialized successfully!")
        return True
    except Exception as e:
        st.error(f"Error initializing system: {str(e)}")
        return False


def process_documents():
    """Process documents and create embeddings"""
    if not st.session_state.system_initialized:
        st.error("Please initialize the system first")
        return False
    
    try:
        with st.spinner("Processing documents... This may take a few minutes."):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Update progress
            progress_bar.progress(25)
            status_text.text("Loading PDF documents...")
            
            # Process documents
            stats = st.session_state.rag_system.load_and_process_documents()
            
            progress_bar.progress(100)
            status_text.text("Processing complete!")
            
            st.session_state.documents_processed = True
            st.session_state.processing_stats = stats
            
        st.success(f"Successfully processed {stats['documents']} documents into {stats['chunks']} chunks in {stats['processing_time']:.2f} seconds")
        return True
        
    except Exception as e:
        st.error(f"Error processing documents: {str(e)}")
        return False


def display_system_status():
    """Display system status in sidebar"""
    st.sidebar.header("System Status")
    
    if st.session_state.system_initialized:
        st.sidebar.success("✅ System Initialized")
        
        if st.session_state.documents_processed:
            st.sidebar.success("✅ Documents Processed")
            stats = st.session_state.processing_stats
            st.sidebar.info(f"📄 {stats.get('documents', 0)} documents")
            st.sidebar.info(f"📝 {stats.get('chunks', 0)} chunks")
        else:
            st.sidebar.warning("⚠️ Documents Not Processed")
    else:
        st.sidebar.error("❌ System Not Initialized")


def display_query_interface():
    """Display the main query interface"""
    st.header("🔍 Query Clinical Trial Documents")
    
    if not st.session_state.documents_processed:
        st.warning("Please process documents first before querying.")
        return
    
    # Query input
    query = st.text_area(
        "Enter your question about clinical trials:",
        placeholder="e.g., What are the common side effects in dermatology clinical trials?",
        height=100
    )
    
    # Query parameters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        top_k = st.slider("Number of results", min_value=1, max_value=10, value=TOP_K_RESULTS)
    
    with col2:
        similarity_threshold = st.slider("Similarity threshold", min_value=0.0, max_value=1.0, value=SIMILARITY_THRESHOLD, step=0.1)
    
    with col3:
        # Get available sources
        try:
            sources = st.session_state.rag_system.get_available_sources()
            source_filter = st.selectbox("Filter by document", ["All documents"] + sources)
            if source_filter == "All documents":
                source_filter = None
        except:
            source_filter = None
    
    # Query button
    if st.button("🚀 Search", type="primary", disabled=not query.strip()):
        if query.strip():
            process_query(query, top_k, similarity_threshold, source_filter)


def process_query(query: str, top_k: int, similarity_threshold: float, source_filter: str):
    """Process user query and display results"""
    try:
        with st.spinner("Searching and generating response..."):
            start_time = time.time()
            
            # Process query
            result = st.session_state.rag_system.query(
                user_query=query,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                source_filter=source_filter
            )
            
            # Add to query history
            st.session_state.query_history.append({
                'query': query,
                'timestamp': time.time(),
                'result': result
            })
        
        # Display results
        display_query_results(result)
        
    except Exception as e:
        st.error(f"Error processing query: {str(e)}")


def display_query_results(result: Dict):
    """Display query results"""
    st.subheader("📋 Results")
    
    # Response
    st.markdown("### 🤖 AI Response")
    st.markdown(result['response'])
    
    # Metadata
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Processing Time", f"{result['processing_time']:.2f}s")
    with col2:
        st.metric("Chunks Retrieved", result['num_chunks_retrieved'])
    with col3:
        st.metric("Sources", len(result['sources']))
    
    # Sources
    if result['sources']:
        st.markdown("### 📚 Sources")
        for source in result['sources']:
            st.markdown(f"- {source}")
    
    # Retrieved chunks (expandable)
    if result['retrieved_chunks']:
        with st.expander(f"📄 View Retrieved Context ({len(result['retrieved_chunks'])} chunks)"):
            for i, chunk in enumerate(result['retrieved_chunks'], 1):
                st.markdown(f"**Chunk {i}** (from {chunk['source']}) - Similarity: {chunk['similarity_score']:.3f}")
                st.markdown(chunk['text'])
                st.markdown("---")


def display_query_history():
    """Display query history"""
    if st.session_state.query_history:
        st.subheader("📜 Query History")
        
        for i, entry in enumerate(reversed(st.session_state.query_history[-5:]), 1):  # Show last 5
            with st.expander(f"Query {len(st.session_state.query_history) - i + 1}: {entry['query'][:50]}..."):
                st.markdown(f"**Query:** {entry['query']}")
                st.markdown(f"**Response:** {entry['result']['response'][:200]}...")
                st.markdown(f"**Sources:** {', '.join(entry['result']['sources'])}")


def main():
    """Main Streamlit application"""
    # Title and description
    st.title(f"{PAGE_ICON} {PAGE_TITLE}")
    st.markdown("""
    This application helps you query and analyze clinical trial research reports using advanced RAG (Retrieval-Augmented Generation) technology.
    
    **Features:**
    - 📄 Process PDF documents from clinical trial reports
    - 🔍 Semantic search through document content
    - 🤖 AI-powered responses using GROQ's LLaMA model
    - 📊 View retrieved context and sources
    """)
    
    # Sidebar
    st.sidebar.title("🛠️ Controls")
    
    # System status
    display_system_status()
    
    # System controls
    st.sidebar.header("System Controls")
    
    if not st.session_state.system_initialized:
        if st.sidebar.button("🚀 Initialize System", type="primary"):
            initialize_system()
    
    if st.session_state.system_initialized and not st.session_state.documents_processed:
        if st.sidebar.button("📄 Process Documents", type="primary"):
            process_documents()
    
    if st.session_state.system_initialized:
        if st.sidebar.button("🔄 Reset System"):
            try:
                st.session_state.rag_system.reset_system()
                st.session_state.system_initialized = False
                st.session_state.documents_processed = False
                st.session_state.processing_stats = {}
                st.sidebar.success("System reset successfully!")
                st.experimental_rerun()
            except Exception as e:
                st.sidebar.error(f"Error resetting system: {str(e)}")
    
    # Main content
    if st.session_state.system_initialized:
        # Tabs for different sections
        tab1, tab2, tab3 = st.tabs(["🔍 Query", "📜 History", "ℹ️ System Info"])
        
        with tab1:
            display_query_interface()
        
        with tab2:
            display_query_history()
        
        with tab3:
            st.subheader("System Information")
            if st.button("🔍 Check System Status"):
                try:
                    status = st.session_state.rag_system.get_system_status()
                    st.json(status)
                except Exception as e:
                    st.error(f"Error getting system status: {str(e)}")
    else:
        st.info("👆 Please initialize the system using the sidebar controls to get started.")
    
    # Footer
    st.markdown("---")
    st.markdown("Built with ❤️ using Streamlit, Qdrant, Hugging Face, and GROQ")


if __name__ == "__main__":
    main()
