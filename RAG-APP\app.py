"""
Clinical Trial RAG Assistant - Main Application
ChatGPT-like interface for querying clinical trial documents using RAG
"""
import streamlit as st
import os
from dotenv import load_dotenv

# Import RAG system
from utils.rag_system import RAGSystem

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="Clinical Trial AI Assistant",
    page_icon="🏥",
    layout="wide"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []

if "rag_system" not in st.session_state:
    st.session_state.rag_system = None

if "system_ready" not in st.session_state:
    st.session_state.system_ready = False

if "uploaded_files" not in st.session_state:
    st.session_state.uploaded_files = []

if "documents_processed" not in st.session_state:
    st.session_state.documents_processed = False

def initialize_rag_system():
    """Initialize the RAG system (without processing documents)"""
    if st.session_state.rag_system is None:
        try:
            with st.spinner("🚀 Initializing AI system..."):
                st.session_state.rag_system = RAGSystem()
                st.session_state.rag_system.initialize()
                st.session_state.system_ready = True

                st.success("✅ System initialized! Please upload documents to start chatting.")
                return True

        except Exception as e:
            st.error(f"❌ Error initializing system: {str(e)}")
            st.error("💡 Make sure you have set your GROQ_API_KEY in the .env file")
            return False
    return True

def process_uploaded_files(uploaded_files):
    """Process uploaded PDF files"""
    if not uploaded_files:
        return False

    try:
        with st.spinner(f"📄 Processing {len(uploaded_files)} documents..."):
            # Save uploaded files temporarily
            import tempfile
            import os
            from utils.document_processor import DocumentProcessor

            temp_dir = tempfile.mkdtemp()
            saved_files = []

            for uploaded_file in uploaded_files:
                if uploaded_file.type == "application/pdf":
                    temp_path = os.path.join(temp_dir, uploaded_file.name)
                    with open(temp_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    saved_files.append(temp_path)

            if not saved_files:
                st.error("No valid PDF files found!")
                return False

            # Process documents using custom processor
            processor = DocumentProcessor()
            documents = []

            for file_path in saved_files:
                try:
                    from pathlib import Path
                    content = processor._extract_text_from_pdf(Path(file_path))
                    if content.strip():
                        documents.append({
                            'filename': os.path.basename(file_path),
                            'filepath': file_path,
                            'content': content,
                            'word_count': len(content.split())
                        })
                except Exception as e:
                    st.warning(f"Could not process {os.path.basename(file_path)}: {str(e)}")

            if not documents:
                st.error("No documents could be processed!")
                return False

            # Create chunks and embeddings
            chunks = processor.chunk_documents(documents)
            embedded_chunks = st.session_state.rag_system.embedding_generator.embed_chunks(chunks)

            # Store in vector database
            st.session_state.rag_system.vector_store.create_collection(recreate=True)
            st.session_state.rag_system.vector_store.add_chunks(embedded_chunks)

            # Clean up temp files
            import shutil
            shutil.rmtree(temp_dir)

            st.session_state.documents_processed = True
            st.session_state.uploaded_files = [f.name for f in uploaded_files]

            st.success(f"✅ Successfully processed {len(documents)} documents with {len(chunks)} chunks!")
            return True

    except Exception as e:
        st.error(f"❌ Error processing documents: {str(e)}")
        return False

def get_rag_response(query):
    """Get response from RAG system"""
    try:
        result = st.session_state.rag_system.query(
            user_query=query,
            top_k=3,  # Reduced to 3 chunks to stay within token limits
            similarity_threshold=0.2,  # Lower threshold for better recall
            include_context=False  # Don't include full context in simple chat
        )
        return result['response'], result.get('sources', [])
    except Exception as e:
        return f"I encountered an error: {str(e)}", []

def main():
    # Custom CSS for better styling
    st.markdown("""
    <style>
    .attach-button {
        background-color: #f0f2f6;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
        margin-top: 0.5rem;
    }
    .upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        background-color: #f9fafb;
    }
    </style>
    """, unsafe_allow_html=True)

    st.title("🏥 Clinical Trial AI Assistant")
    st.markdown("Upload your clinical trial documents and ask questions about them")

    # Check API key
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key or api_key == "your_groq_api_key_here":
        st.error("❌ Please set your GROQ_API_KEY in the .env file")
        st.stop()

    # Initialize system if not ready
    if not st.session_state.system_ready:
        if not initialize_rag_system():
            st.stop()

    # Document upload section
    if not st.session_state.documents_processed:
        st.markdown("### 📄 Upload Documents")
        st.markdown("Drag and drop up to 10 PDF documents to get started:")

        uploaded_files = st.file_uploader(
            "Choose PDF files",
            type="pdf",
            accept_multiple_files=True,
            help="Upload up to 10 PDF documents (clinical trial reports, research papers, etc.)"
        )

        if uploaded_files:
            if len(uploaded_files) > 10:
                st.warning("⚠️ Please upload maximum 10 documents. Only the first 10 will be processed.")
                uploaded_files = uploaded_files[:10]

            st.markdown(f"**Selected Files ({len(uploaded_files)}):**")
            for file in uploaded_files:
                st.markdown(f"• {file.name} ({file.size / 1024:.1f} KB)")

            if st.button("🚀 Process Documents", type="primary"):
                if process_uploaded_files(uploaded_files):
                    st.rerun()

        st.markdown("---")
        st.info("👆 Please upload and process documents before you can start chatting!")
        return
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            
            # Show sources for assistant messages
            if message["role"] == "assistant" and "sources" in message:
                if message["sources"]:
                    with st.expander("📚 Sources"):
                        for source in message["sources"]:
                            st.markdown(f"• {source}")
    
    # Chat interface with attach functionality
    col1, col2 = st.columns([0.9, 0.1])

    with col1:
        prompt = st.chat_input("Ask about clinical trials...")

    with col2:
        # Attach button for additional documents
        if st.button("📎", help="Upload additional documents", key="attach_btn"):
            st.session_state.show_upload = True

    # Show upload dialog if attach button clicked
    if st.session_state.get("show_upload", False):
        with st.expander("📎 Upload Additional Documents", expanded=True):
            new_files = st.file_uploader(
                "Add more PDF documents",
                type="pdf",
                accept_multiple_files=True,
                key="additional_files",
                help="Upload additional PDF documents to expand your knowledge base"
            )

            col_a, col_b = st.columns(2)
            with col_a:
                if st.button("➕ Add Documents") and new_files:
                    if process_uploaded_files(new_files):
                        st.session_state.uploaded_files.extend([f.name for f in new_files])
                        st.success(f"Added {len(new_files)} more documents!")
                        st.session_state.show_upload = False
                        st.rerun()

            with col_b:
                if st.button("❌ Cancel"):
                    st.session_state.show_upload = False
                    st.rerun()

    # Process chat input
    if prompt:
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)

        # Get RAG response
        with st.chat_message("assistant"):
            with st.spinner("Searching documents and generating response..."):
                response, sources = get_rag_response(prompt)
                st.markdown(response)

                # Show sources
                if sources:
                    with st.expander("📚 Sources"):
                        for source in sources:
                            st.markdown(f"• {source}")

        # Add assistant response to chat history
        st.session_state.messages.append({
            "role": "assistant",
            "content": response,
            "sources": sources
        })
    
    # Sidebar
    with st.sidebar:
        st.header("💬 Chat Controls")
        
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()
        
        st.markdown("---")
        
        # System status
        if st.session_state.system_ready:
            if st.session_state.documents_processed:
                st.success("✅ System Ready")
                st.markdown(f"**Documents:** {len(st.session_state.uploaded_files)}")
                st.markdown(f"**Messages:** {len(st.session_state.messages)}")

                with st.expander("📄 Uploaded Documents"):
                    for doc in st.session_state.uploaded_files:
                        st.markdown(f"• {doc}")
            else:
                st.info("📄 Upload documents to start")
        else:
            st.warning("⚠️ System Initializing...")

        # Document management
        if st.session_state.documents_processed:
            st.markdown("---")
            if st.button("🔄 Reset Documents"):
                st.session_state.documents_processed = False
                st.session_state.uploaded_files = []
                st.session_state.messages = []
                if st.session_state.rag_system:
                    st.session_state.rag_system.vector_store.create_collection(recreate=True)
                st.success("Documents reset! Upload new documents to continue.")
                st.rerun()
        
        st.markdown("---")
        st.markdown("**Example Questions:**")
        st.markdown("• What are common side effects?")
        st.markdown("• How long do trials typically last?")
        st.markdown("• What safety measures are used?")
        st.markdown("• What are the inclusion criteria?")

        # Debug section
        if st.checkbox("🔍 Debug Mode"):
            st.markdown("**Debug Info:**")
            if st.session_state.system_ready:
                try:
                    status = st.session_state.rag_system.get_system_status()
                    st.json(status)
                except Exception as e:
                    st.error(f"Debug error: {e}")

if __name__ == "__main__":
    main()
