"""
Clinical Trial RAG Assistant - Main Application
ChatGPT-like interface for querying clinical trial documents using RAG
"""
import streamlit as st
import os
from dotenv import load_dotenv

# Import RAG system
from utils.rag_system import RAGSystem

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="Clinical Trial AI Assistant",
    page_icon="🏥",
    layout="wide"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []

if "rag_system" not in st.session_state:
    st.session_state.rag_system = None

if "system_ready" not in st.session_state:
    st.session_state.system_ready = False

def initialize_rag_system():
    """Initialize the RAG system"""
    if st.session_state.rag_system is None:
        try:
            with st.spinner("🚀 Initializing AI system... This may take a few minutes on first run."):
                st.session_state.rag_system = RAGSystem()
                st.session_state.rag_system.initialize()
                
                # Process documents if not already done
                stats = st.session_state.rag_system.load_and_process_documents()
                st.session_state.system_ready = True
                
                st.success(f"✅ System ready! Processed {stats['documents']} documents with {stats['chunks']} chunks.")
                return True
                
        except Exception as e:
            st.error(f"❌ Error initializing system: {str(e)}")
            st.error("💡 Make sure you have:")
            st.error("1. Set your GROQ_API_KEY in the .env file")
            st.error("2. PDF documents in the 'Clinical Trial Research Reports' folder")
            return False
    return True

def get_rag_response(query):
    """Get response from RAG system"""
    try:
        result = st.session_state.rag_system.query(
            user_query=query,
            top_k=3,  # Reduced to 3 chunks to stay within token limits
            similarity_threshold=0.2,  # Lower threshold for better recall
            include_context=False  # Don't include full context in simple chat
        )
        return result['response'], result.get('sources', [])
    except Exception as e:
        return f"I encountered an error: {str(e)}", []

def main():
    st.title("🏥 Clinical Trial AI Assistant")
    st.markdown("Ask questions about clinical trial research documents")
    
    # Check API key
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key or api_key == "your_groq_api_key_here":
        st.error("❌ Please set your GROQ_API_KEY in the .env file")
        st.stop()
    
    # Initialize system if not ready
    if not st.session_state.system_ready:
        if not initialize_rag_system():
            st.stop()
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            
            # Show sources for assistant messages
            if message["role"] == "assistant" and "sources" in message:
                if message["sources"]:
                    with st.expander("📚 Sources"):
                        for source in message["sources"]:
                            st.markdown(f"• {source}")
    
    # Chat input
    if prompt := st.chat_input("Ask about clinical trials..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Get RAG response
        with st.chat_message("assistant"):
            with st.spinner("Searching documents and generating response..."):
                response, sources = get_rag_response(prompt)
                st.markdown(response)
                
                # Show sources
                if sources:
                    with st.expander("📚 Sources"):
                        for source in sources:
                            st.markdown(f"• {source}")
        
        # Add assistant response to chat history
        st.session_state.messages.append({
            "role": "assistant", 
            "content": response,
            "sources": sources
        })
    
    # Sidebar
    with st.sidebar:
        st.header("💬 Chat Controls")
        
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()
        
        st.markdown("---")
        
        # System status
        if st.session_state.system_ready:
            st.success("✅ System Ready")
            
            # Get system info
            try:
                sources = st.session_state.rag_system.get_available_sources()
                st.markdown(f"**Documents:** {len(sources)}")
                st.markdown(f"**Messages:** {len(st.session_state.messages)}")
                
                with st.expander("📄 Available Documents"):
                    for source in sources:
                        st.markdown(f"• {source}")
                        
            except Exception:
                pass
        else:
            st.warning("⚠️ System Initializing...")
        
        st.markdown("---")
        st.markdown("**Example Questions:**")
        st.markdown("• What are common side effects?")
        st.markdown("• How long do trials typically last?")
        st.markdown("• What safety measures are used?")
        st.markdown("• What are the inclusion criteria?")

        # Debug section
        if st.checkbox("🔍 Debug Mode"):
            st.markdown("**Debug Info:**")
            if st.session_state.system_ready:
                try:
                    status = st.session_state.rag_system.get_system_status()
                    st.json(status)
                except Exception as e:
                    st.error(f"Debug error: {e}")

if __name__ == "__main__":
    main()
