"""
Configuration settings for the RAG application
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
GROQ_API_KEY = os.getenv("GROQ_API_KEY")

# Model Configuration
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
LLM_MODEL = "meta-llama/llama-4-scout-17b-16e-instruct"

# Qdrant Configuration
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
COLLECTION_NAME = "clinical_trials"
VECTOR_SIZE = 384  # all-MiniLM-L6-v2 embedding dimension

# Document Processing Configuration
DOCUMENTS_FOLDER = "Clinical Trial Research Reports"
CHUNK_SIZE = 1000  # Maximum characters per chunk
CHUNK_OVERLAP = 200  # Overlap between chunks

# Retrieval Configuration
TOP_K_RESULTS = 5  # Number of similar chunks to retrieve
SIMILARITY_THRESHOLD = 0.3  # Minimum similarity score

# Streamlit Configuration
PAGE_TITLE = "Clinical Trial RAG Assistant"
PAGE_ICON = "🏥"

# Few-shot prompting examples
FEW_SHOT_EXAMPLES = [
    {
        "query": "What are the common side effects in dermatology clinical trials?",
        "context": "In dermatology trials, patients commonly reported mild skin irritation, redness, and dryness at the application site.",
        "response": "Based on the clinical trial data, the most common side effects in dermatology trials include mild skin irritation (reported in 15-20% of patients), localized redness at the application site (10-15% of patients), and skin dryness (8-12% of patients). These effects are typically mild and resolve within a few days of treatment discontinuation."
    },
    {
        "query": "What is the typical duration of dermatology clinical trials?",
        "context": "The study protocol specified a 12-week treatment period with follow-up assessments at weeks 16 and 24.",
        "response": "According to the clinical trial protocols, dermatology studies typically run for 12-16 weeks of active treatment, with additional follow-up periods extending to 24 weeks to monitor long-term effects and safety outcomes."
    }
]
