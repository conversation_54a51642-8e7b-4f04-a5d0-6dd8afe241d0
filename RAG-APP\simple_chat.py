"""
Simple ChatGPT-like interface using GROQ API
"""
import streamlit as st
import os
from groq import Groq
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="Simple AI Chat",
    page_icon="💬",
    layout="wide"
)

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []

if "groq_client" not in st.session_state:
    st.session_state.groq_client = None

def initialize_groq():
    """Initialize GROQ client"""
    api_key = os.getenv("GROQ_API_KEY")
    
    if not api_key:
        st.error("❌ GROQ API key not found. Please set GROQ_API_KEY in your .env file")
        st.stop()
    
    if api_key == "your_groq_api_key_here":
        st.error("❌ Please replace the placeholder GROQ API key in your .env file with your actual key")
        st.stop()
    
    try:
        st.session_state.groq_client = Groq(api_key=api_key)
        return True
    except Exception as e:
        st.error(f"❌ Error initializing GROQ client: {e}")
        return False

def get_ai_response(messages):
    """Get response from GROQ API"""
    try:
        completion = st.session_state.groq_client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=messages,
            temperature=0.7,
            max_completion_tokens=1024,
            top_p=0.9,
            stream=False
        )
        return completion.choices[0].message.content
    except Exception as e:
        return f"Error: {str(e)}"

def main():
    st.title("💬 Simple AI Chat")
    st.markdown("Chat with AI using GROQ's LLaMA model")
    
    # Initialize GROQ client if not already done
    if st.session_state.groq_client is None:
        if not initialize_groq():
            return
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Type your message here..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Get AI response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = get_ai_response(st.session_state.messages)
                st.markdown(response)
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})
    
    # Sidebar with controls
    with st.sidebar:
        st.header("Chat Controls")
        
        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()
        
        st.markdown("---")
        st.markdown("**Model:** meta-llama/llama-4-scout-17b-16e-instruct")
        st.markdown(f"**Messages:** {len(st.session_state.messages)}")
        
        # API key status
        api_key = os.getenv("GROQ_API_KEY")
        if api_key and api_key != "your_groq_api_key_here":
            st.success("✅ API Key Configured")
        else:
            st.error("❌ API Key Not Set")

if __name__ == "__main__":
    main()
