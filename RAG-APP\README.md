# Clinical Trial RAG Assistant 🏥

A ChatGPT-like interface for querying clinical trial research documents using RAG (Retrieval-Augmented Generation) technology.

## Features ✨

- 🤖 **ChatGPT-like Interface** - Simple chat interface for natural conversations
- 📎 **Drag & Drop Upload** - Upload up to 10 PDF documents of your choice
- 📄 **Document Processing** - Automatically processes uploaded PDF documents
- 🔍 **Semantic Search** - Finds relevant information using AI embeddings
- 🧠 **AI-Powered Responses** - Uses GROQ's LLaMA model for intelligent answers
- 📚 **Source Citations** - Shows which documents the answers came from
- ➕ **Add More Documents** - Attach button to add additional documents anytime
- 💾 **Vector Database** - Efficient storage and retrieval using Qdrant

## Quick Start 🚀

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Set Up API Key
1. Copy `.env.template` to `.env`
2. Get your GROQ API key from [https://console.groq.com/](https://console.groq.com/)
3. Add your key to `.env`:
```
GROQ_API_KEY=your_actual_groq_api_key_here
```

### 3. Run the Application
```bash
streamlit run app.py
```

The app will automatically:
- Load the AI embedding model
- Set up the vector database
- Show the document upload interface
- Process your uploaded documents
- Start the chat interface

## Usage 💬

### 1. Upload Documents 📄
- Drag and drop up to 10 PDF files into the upload area
- Click "🚀 Process Documents" to analyze them
- Wait for processing to complete

### 2. Start Chatting 💬
Once documents are processed, you can ask questions like:

- "What are the common side effects in dermatology clinical trials?"
- "How long do clinical trials typically last?"
- "What safety measures are mentioned in the studies?"
- "What are the inclusion criteria for participants?"
- "What endpoints are used to measure efficacy?"

The AI will search through your uploaded documents and provide answers based on the actual content, along with source citations.

### 3. Add More Documents 📎
- Use the 📎 attach button next to the chat input
- Upload additional PDF documents anytime
- The system will process and add them to your knowledge base

## Technical Details 🔧

### Architecture
- **Frontend**: Streamlit web interface
- **Embeddings**: Hugging Face sentence-transformers (all-MiniLM-L6-v2)
- **Vector Database**: Qdrant (in-memory fallback)
- **LLM**: GROQ API with meta-llama/llama-4-scout-17b-16e-instruct
- **Document Processing**: PyPDF2 and pdfplumber for PDF extraction

### Document Processing Pipeline
1. **PDF Loading** - Extracts text from PDF documents
2. **Chunking** - Splits documents into paragraph-based chunks
3. **Embedding Generation** - Converts text to 384-dimensional vectors
4. **Vector Storage** - Stores embeddings in Qdrant database
5. **Similarity Search** - Finds relevant chunks for user queries
6. **Response Generation** - Uses retrieved context with LLM

### Configuration
Key settings in `config.py`:
- `SIMILARITY_THRESHOLD = 0.3` - Minimum similarity for search results
- `TOP_K_RESULTS = 5` - Number of chunks to retrieve
- `CHUNK_SIZE = 1000` - Maximum characters per chunk

## File Structure 📁

```
RAG-APP/
├── app.py                          # Main Streamlit application
├── config.py                       # Configuration settings
├── requirements.txt                # Python dependencies
├── .env                           # Environment variables (create from template)
├── Clinical Trial Research Reports/ # PDF documents folder
└── utils/                         # Core modules
    ├── document_processor.py      # PDF processing and chunking
    ├── embeddings.py             # Text embedding generation
    ├── vector_store.py           # Qdrant vector database
    ├── llm_client.py             # GROQ API integration
    └── rag_system.py             # Main RAG orchestration
```

## Troubleshooting 🔧

### Common Issues

**"No relevant information found"**
- The similarity threshold might be too high
- Try rephrasing your question
- Check if your documents contain information about the topic

**"Error initializing system"**
- Make sure your GROQ_API_KEY is set correctly in `.env`
- Ensure PDF documents are in the "Clinical Trial Research Reports" folder
- Check your internet connection for downloading the embedding model

**"Connection refused" (Qdrant)**
- This is normal - the app will use in-memory storage as fallback
- Your data won't persist between sessions, but everything else works fine

### Debug Mode
Enable "🔍 Debug Mode" in the sidebar to see:
- System status information
- Vector database statistics
- Model information

## Requirements 📋

- Python 3.8+
- Internet connection (for first-time model download)
- GROQ API key
- PDF documents in the specified folder

## License 📄

Built with ❤️ using Streamlit, Qdrant, Hugging Face, and GROQ.
